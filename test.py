import requests
import time
import os
import logging
from concurrent.futures import Thread<PERSON>oolExecutor
from colorama import init, Fore
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# 初始化colorama
init()

# 配置日志 - 只写入文件，不输出到控制台
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("coupon_checker.log")  # 移除了StreamHandler，只保留文件处理器
    ]
)
logger = logging.getLogger(__name__)

# 抑制urllib3的日志
logging.getLogger("urllib3").setLevel(logging.WARNING)

# 配置参数
CONFIG = {
    "input_file": "code1.txt",
    "deduplicated_file": "code1_deduplicated.txt",
    "success_file": "success1.txt",
    "error_file": "error1.txt",
    "max_workers": 10,
    "timeout": 10,
    "retry_times": 3,
    "sleep_time": 0.1,
    # 代理配置
    "proxy": {
        "enabled": True,
        "type": "socks5",  # 可选: http, https, socks5 (需要安装PySocks)
        "host": "127.0.0.1",
        "port": "7890",
        "username": "",  # 如果需要认证
        "password": ""   # 如果需要认证
    }
}

# 线程锁，用于防止输出混乱
import threading
print_lock = threading.Lock()

def load_codes_from_file(filename=None):
    """从文件中加载优惠码"""
    if filename is None:
        filename = CONFIG["input_file"]
    codes = []
    try:
        with open(filename, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if line and "code=" in line:
                    code = line.split("code=")[1]
                    codes.append(code)
    except FileNotFoundError:
        logger.error(f"{filename}文件不存在")
    except Exception as e:
        logger.error(f"读取{filename}文件失败: {str(e)}")
    return codes

def deduplicate_codes(codes):
    """去重优惠码并统计去重前后数量"""
    original_count = len(codes)
    unique_codes = list(dict.fromkeys(codes))  # 保持原始顺序的去重
    duplicate_count = original_count - len(unique_codes)
    
    logger.info(f"去重完成: 原始数量 {original_count} 个，去重后 {len(unique_codes)} 个，移除重复 {duplicate_count} 个")
    print(f"去重完成: 原始数量 {original_count} 个，去重后 {len(unique_codes)} 个，移除重复 {duplicate_count} 个")
    
    return unique_codes

def save_deduplicated_codes(codes, filename=None):
    """保存去重后的优惠码到文件"""
    if filename is None:
        filename = CONFIG["deduplicated_file"]
    try:
        with open(filename, "w", encoding="utf-8") as f:
            for code in codes:
                f.write(f"code={code}\n")
        logger.info(f"去重后的优惠码已保存到 {filename}")
        print(f"去重后的优惠码已保存到 {filename}")
        return True
    except Exception as e:
        logger.error(f"保存去重文件失败: {str(e)}")
        print(f"保存去重文件失败: {str(e)}")
        return False

def create_session():
    """创建并配置Session对象"""
    session = requests.Session()
    
    # 配置重试机制 - 设置为静默重试，不输出警告
    retry_strategy = Retry(
        total=CONFIG["retry_times"],
        backoff_factor=1,
        status_forcelist=[429, 500, 502, 503, 504],
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("https://", adapter)
    session.mount("http://", adapter)
    
    # 配置代理
    if CONFIG["proxy"]["enabled"]:
        proxy_type = CONFIG["proxy"]["type"]
        host = CONFIG["proxy"]["host"]
        port = CONFIG["proxy"]["port"]
        user = CONFIG["proxy"]["username"]
        pwd = CONFIG["proxy"]["password"]
        
        # 构建代理URL
        if user and pwd:
            proxy_url = f"{proxy_type}://{user}:{pwd}@{host}:{port}"
        else:
            proxy_url = f"{proxy_type}://{host}:{port}"
        
        session.proxies = {
            "http": proxy_url,
            "https": proxy_url
        }
        # 只在日志中记录一次，不打印到控制台
        logger.info(f"配置代理: {proxy_type}://{host}:{port}")
    
    return session

def check_code(session, code):
    """检测优惠码是否有效"""
    url = "https://www.cursor.com/api/dashboard/check-referral-code"
    headers = {
        "accept": "*/*",
        "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "content-type": "application/json",
        "priority": "u=1, i",
        "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
        "sec-ch-ua-arch": "\"x86\"",
        "sec-ch-ua-bitness": "\"64\"",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "\"Windows\"",
        "sec-ch-ua-platform-version": "\"19.0.0\"",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-origin",
        "Referer": f"https://www.cursor.com/cn/referral?code={code}",
        "Referrer-Policy": "strict-origin-when-cross-origin"
    }
    payload = {"referralCode": code}
    
    try:
        response = session.post(url, headers=headers, json=payload, timeout=CONFIG["timeout"])
        result = response.json()
        is_valid = result.get("isValid", False)
        
        # 使用锁确保输出不会混乱
        with print_lock:
            color = Fore.GREEN if is_valid else Fore.RED
            print(f"优惠码----{code}----isValid:{color}{is_valid}{Fore.RESET}")
        
        # 记录到日志文件，不显示在控制台
        status_code = response.status_code
        logger.info(f"优惠码----{code}----状态码----{status_code}----isValid:{is_valid}")
        
        # 保存结果到对应文件
        save_result(code, is_valid)
            
        return is_valid, code, result
    except requests.exceptions.ConnectTimeout:
        # 只记录日志，不打印到控制台
        logger.warning(f"优惠码----{code}----连接超时")
        save_result(code, False)
        return False, code, None
    except requests.exceptions.ProxyError:
        # 只记录日志，不打印到控制台
        logger.error(f"优惠码----{code}----代理错误，请检查代理设置")
        save_result(code, False)
        return False, code, None
    except Exception as e:
        # 只记录日志，不打印到控制台
        logger.error(f"优惠码----{code}----检测失败: {str(e)}")
        save_result(code, False)
        return False, code, None

def save_result(code, is_valid):
    """保存检测结果到对应文件"""
    if is_valid:
        with open(CONFIG["success_file"], "a", encoding="utf-8") as f:
            f.write(f"{code}\n")
        logger.info(f"已保存有效优惠码: {code}")
    else:
        with open(CONFIG["error_file"], "a", encoding="utf-8") as f:
            f.write(f"{code}\n")
        logger.info(f"已保存失败优惠码: {code}")

def check_worker(code):
    """工作线程函数"""
    # 为每个工作线程创建一个单独的会话
    session = create_session()
    return check_code(session, code)

def main():
    """主函数，使用线程池执行优惠码检测"""
    # 确保文件存在
    if not os.path.exists(CONFIG["success_file"]):
        with open(CONFIG["success_file"], "w", encoding="utf-8") as f:
            f.write("")
    if not os.path.exists(CONFIG["error_file"]):
        with open(CONFIG["error_file"], "w", encoding="utf-8") as f:
            f.write("")
    
    print("=" * 60)
    print("第一步：加载原始优惠码并去重")
    print("=" * 60)
    
    # 加载原始优惠码
    original_codes = load_codes_from_file()
    if not original_codes:
        print("code1.txt文件中没有找到有效的优惠码")
        return
    
    # 去重
    deduplicated_codes = deduplicate_codes(original_codes)
    
    # 保存去重后的优惠码
    if not save_deduplicated_codes(deduplicated_codes):
        print("保存去重文件失败，程序退出")
        return
    
    print("\n" + "=" * 60)
    print("第二步：校验去重后的优惠码")
    print("=" * 60)
    
    # 使用去重后的文件进行校验
    codes_to_check = load_codes_from_file(CONFIG["deduplicated_file"])
    if not codes_to_check:
        print("去重后文件中没有找到有效的优惠码")
        return
    
    # 检测信息
    print(f"开始检测优惠码，成功的存入: {CONFIG['success_file']}，失败的存入: {CONFIG['error_file']}")
    print(f"共找到 {len(codes_to_check)} 个去重后的优惠码需要检测")
    
    # 只在开始时显示一次代理信息
    if CONFIG["proxy"]["enabled"]:
        proxy_type = CONFIG["proxy"]["type"].upper()
        host = CONFIG["proxy"]["host"]
        port = CONFIG["proxy"]["port"]
        print(f"使用{proxy_type}代理: {host}:{port}")
    
    print("=" * 60)
    logger.info(f"开始检测优惠码，成功的存入: {CONFIG['success_file']}，失败的存入: {CONFIG['error_file']}")
    
    # 使用线程池
    success_count = 0
    attempt_count = 0
    
    try:
        with ThreadPoolExecutor(max_workers=CONFIG["max_workers"]) as executor:
            # 提交所有优惠码检测任务
            futures = [executor.submit(check_worker, code) for code in codes_to_check]
            
            # 等待所有任务完成并收集结果
            for future in futures:
                is_valid, _, _ = future.result()
                attempt_count += 1
                if is_valid:
                    success_count += 1
                
                # 每10次检测显示统计信息
                if attempt_count % 10 == 0 or attempt_count == len(codes_to_check):
                    success_rate = (success_count / attempt_count) * 100 if attempt_count > 0 else 0
                    logger.info(f"统计: 已检测 {attempt_count} 个码，找到 {success_count} 个有效码，成功率 {success_rate:.2f}%")
                    with print_lock:
                        print(f"\n{Fore.CYAN}进度: {attempt_count}/{len(codes_to_check)} - 找到 {success_count} 个有效码，成功率 {success_rate:.2f}%{Fore.RESET}\n")
                
                # 速率控制
                time.sleep(CONFIG["sleep_time"])
                
        # 显示最终统计
        if attempt_count > 0:
            success_rate = (success_count / attempt_count) * 100
            print(f"{Fore.CYAN}检测完成! 共检测 {attempt_count} 个码，找到 {success_count} 个有效码，成功率 {success_rate:.2f}%{Fore.RESET}")
            
    except KeyboardInterrupt:
        logger.info("程序已手动停止")
        print(f"\n{Fore.YELLOW}程序已停止{Fore.RESET}")
        # 显示最终统计
        if attempt_count > 0:
            success_rate = (success_count / attempt_count) * 100
            print(f"{Fore.CYAN}最终统计: 共检测 {attempt_count} 个码，找到 {success_count} 个有效码，成功率 {success_rate:.2f}%{Fore.RESET}")

if __name__ == "__main__":
    main()